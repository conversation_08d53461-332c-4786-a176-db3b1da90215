# =============================================================================
# MyMaid-EC10 Environment Configuration Template
# =============================================================================
# Copy file này thành .env và điền các giá trị thực tế của bạn
# KHÔNG BAO GIỜ commit file .env vào git!

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=5000

# =============================================================================
# FIREBASE CONFIGURATION - SERVER SIDE
# =============================================================================
# Lấy từ Firebase Console > Project Settings > Service accounts > Generate new private key
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your-project.iam.gserviceaccount.com

# =============================================================================
# FIREBASE CONFIGURATION - CLIENT SIDE (REACT)
# =============================================================================
# Lấy từ Firebase Console > Project Settings > Web app config
REACT_APP_FIREBASE_API_KEY=your-firebase-web-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-firebase-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
REACT_APP_FIREBASE_APP_ID=your-app-id
REACT_APP_FIREBASE_MEASUREMENT_ID=your-measurement-id

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-here-make-it-very-long-and-random
JWT_EXPIRE=7d

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
FRONTEND_URL=http://localhost:3000

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGIN=http://localhost:3000

# =============================================================================
# API KEYS (OPTIONAL)
# =============================================================================
GEMINI_API_KEY=your-gemini-api-key
VIRUSTOTAL_API_KEY=your-virustotal-api-key
SCAMADVISER_API_KEY=your-scamadviser-api-key
SCREENSHOTLAYER_API_KEY=your-screenshotlayer-api-key
NEWSAPI_API_KEY=your-newsapi-api-key
NEWSDATA_API_KEY=your-newsdata-api-key

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100