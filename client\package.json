{"name": "factcheck-frontend", "version": "1.0.0", "description": "FactCheck Frontend Application", "private": true, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "axios": "^1.5.0", "bootstrap": "^5.3.6", "clsx": "^2.1.1", "config": "^4.1.0", "cors": "^2.8.5", "express": "^5.1.0", "firebase": "^12.0.0", "framer-motion": "^12.23.12", "gsap": "^3.13.0", "js-cookie": "^3.0.5", "lottie-react": "^2.4.1", "lucide-react": "^0.536.0", "mapbox-gl": "^3.14.0", "postcss": "^8.5.6", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.5.2", "react-map-gl": "^8.0.4", "react-query": "^3.39.3", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "react-spring": "^10.0.1", "styled-components": "^6.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "yup": "^1.3.2"}, "devDependencies": {"@types/js-cookie": "^3.0.3", "web-vitals": "^3.4.0"}, "scripts": {"start": "set PORT=3000 && react-scripts start", "start:3000": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "stop": "taskkill /f /im node.exe 2>nul && netstat -ano | findstr :3000 | findstr LISTENING >nul && for /f \"tokens=5\" %a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do taskkill /f /pid %a 2>nul && echo \"Cleared port 3000\" || echo \"Port 3000 is free\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}