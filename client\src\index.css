@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* Custom base styles */
@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
    background-color: #f8fafc;
    color: #1c1949;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', 'Inter', sans-serif;
    font-weight: 600;
    color: #1c1949;
    line-height: 1.2;
  }
}

/* Custom components */
@layer components {
  .container-custom {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-md border border-white/20;
  }

  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-2 hover:shadow-large;
  }
}

/* Mapbox Styles */
.mapboxgl-map {
  font-family: 'Inter', sans-serif;
}

.mapboxgl-popup-content {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mapboxgl-popup-close-button {
  padding: 8px;
  font-size: 18px;
  color: #6b7280;
}

.mapboxgl-popup-close-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* Marker Animation */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.mapbox-marker {
  transition: all 0.3s ease;
}

.mapbox-marker:hover {
  transform: scale(1.1);
}

/* Custom utilities */
@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Legacy Bootstrap compatibility - keeping for gradual migration */
.btn {
  @apply inline-flex items-center justify-center px-6 py-3 border-0 rounded-lg text-base font-medium cursor-pointer transition-all duration-200 min-h-[44px];
}

.btn:disabled {
  @apply opacity-60 cursor-not-allowed;
}

.btn-primary {
  @apply bg-primary-500 text-white hover:bg-primary-600;
}

.btn-secondary {
  @apply bg-secondary-500 text-neutral-900 hover:bg-secondary-600;
}

.btn-danger {
  @apply bg-red-500 text-white hover:bg-red-600;
}

.btn-outline {
  @apply bg-transparent border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white;
}

.form-group {
  @apply mb-6;
}

.form-label {
  @apply block mb-2 font-medium text-neutral-900;
}

.form-input {
  @apply w-full px-3 py-2 border-2 border-neutral-300 rounded-lg text-base transition-colors focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20;
}

.form-input.error {
  @apply border-red-500;
}

.form-error {
  @apply text-red-500 text-sm mt-1;
}

/* Legacy card styles */
.card {
  @apply bg-white rounded-xl shadow-soft border border-neutral-200 p-6 transition-transform duration-200 hover:-translate-y-1;
}

.grid-auto {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
}

.loading {
  @apply flex items-center justify-center p-8;
}

.spinner {
  @apply w-8 h-8 border-4 border-neutral-200 border-t-primary-500 rounded-full animate-spin;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

/* Legacy banner styles */
.banner-wrapper {
  @apply w-screen -ml-[50vw] ml-[50%] overflow-hidden;
}

.banner-img {
  @apply h-[600px] object-cover rounded-lg;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .container-custom {
    @apply px-3;
  }

  .btn {
    @apply text-sm px-5 py-2;
  }

  .card {
    @apply p-4;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-neutral-900;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Global styles for better UX */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus styles for better accessibility */
*:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Button focus styles */
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Input focus styles */
input:focus {
  outline: none;
}

/* Animation utilities */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Loading animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Smooth transitions for all elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease;
}

/* Disable transitions for specific elements */
.no-transition {
  transition: none !important;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Responsive text */
@media (max-width: 640px) {
  .responsive-text {
    font-size: 0.875rem;
  }
}

/* Custom focus ring */
.focus-ring:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Disable text selection for certain elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Custom button styles */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

/* Form input enhancements */
.input-modern {
  transition: all 0.3s ease;
}

.input-modern:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* ================================================================== */
/*  Responsive Adjustments for Full HD (1920px) and larger screens  */
/* ================================================================== */

@media (min-width: 1920px) {
  
  /* Tăng kích thước font chữ cơ bản cho toàn bộ trang */
  body {
    font-size: 18px; /* Tăng từ 16px (mặc định) lên 18px */
  }

  /* Tăng kích thước cho các container chính để nội dung không bị quá hẹp */
  .container-custom,
  .max-w-7xl { /* Áp dụng cho cả class của Tailwind */
    max-width: 1440px; /* Tăng từ 1200px hoặc 1280px lên 1440px */
  }

  /* Điều chỉnh lại các component cụ thể */
  @layer components {
    
    /* Tăng kích thước nút bấm */
    .btn {
      @apply px-8 py-4 text-lg; /* Tăng padding và kích thước chữ */
    }

    /* Tăng kích thước card */
    .card {
      @apply p-8; /* Tăng padding từ p-6 lên p-8 */
    }

    /* Tăng kích thước font cho các tiêu đề trong card */
    .card-title {
      @apply text-2xl;
    }
  }

  /* Điều chỉnh lại các lớp tiện ích cụ thể */
  @layer utilities {
    
    /* Tăng kích thước cho các tiêu đề lớn */
    .text-5xl {
      @apply text-6xl;
    }
    
    .text-7xl {
      @apply text-8xl;
    }
  }

  /* Điều chỉnh riêng cho các trang cụ thể nếu cần */
  
  /* Ví dụ: Tăng khoảng cách trong Hero Section */
  .hero-section {
    padding-top: 10rem;
    padding-bottom: 10rem;
  }

  /* Ví dụ: Tăng kích thước font cho các mục highlights */
  .highlight-item h3 {
    font-size: 1.5rem; /* text-2xl */
  }
  .highlight-item p {
    font-size: 1.125rem; /* text-lg */
  }
}