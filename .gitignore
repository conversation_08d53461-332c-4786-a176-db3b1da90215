# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
build/
dist/
*.tgz
*.tar.gz

# Runtime data
pids
*.pid
*.seed
*.pid.lock
.mymaid-pids.json

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Docker
.dockerignore

# Temporary files
tmp/
temp/

# Firebase Service Account Keys and Credentials (SECURITY CRITICAL)
*firebase*adminsdk*.json
*serviceAccount*.json
*service-account*.json
*firebase-adminsdk*.json
server/serviceAccountKey.json

# Environment files with real credentials
.env.backup
.env.production
.env.staging
*.key
*.pem
*.p12

# Any file with "key" or "secret" in the name
*secret*
*private*key*
*credentials*