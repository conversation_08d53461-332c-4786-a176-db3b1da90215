import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { CheckCircle, XCircle, Mail, ArrowRight } from 'lucide-react';
import styled from 'styled-components';
import LoadingSpinner from '../components/LoadingSpinner';

const VerifyContainer = styled.div`
  min-height: calc(100vh - 4rem);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  background: #f8fafc;
`;

const VerifyCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  text-align: center;
`;

const IconContainer = styled.div`
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  background: ${props => props.success ? '#dcfce7' : props.error ? '#fecaca' : '#e0e7ff'};
  color: ${props => props.success ? '#16a34a' : props.error ? '#dc2626' : '#3b82f6'};
`;

const Title = styled.h1`
  font-size: 1.875rem;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 1rem;
`;

const Message = styled.p`
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
`;

const ActionButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: background-color 0.2s;

  &:hover {
    background: #2563eb;
  }
`;

const VerifyEmailPage = () => {
  const { user } = useAuth();
  const [status, setStatus] = useState('loading'); // loading, success, error
  const [message, setMessage] = useState('');

  useEffect(() => {
    // Kiểm tra user có tồn tại không
    if (!user) {
      setStatus('error');
      setMessage('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập trước.');
      return;
    }

    // Kiểm tra email đã được verify chưa
    if (user.emailVerified) {
      setStatus('success');
      setMessage('Email của bạn đã được xác thực thành công! Bây giờ bạn có thể sử dụng đầy đủ các tính năng của hệ thống.');
    } else {
      setStatus('error');
      setMessage('Email của bạn chưa được xác thực. Vui lòng kiểm tra email và nhấp vào liên kết xác thực.');
    }
  }, [user]);

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <>
            <IconContainer>
              <Mail size={32} />
            </IconContainer>
            <Title>Đang kiểm tra...</Title>
            <Message>Vui lòng đợi trong giây lát</Message>
            <LoadingSpinner />
          </>
        );

      case 'success':
        return (
          <>
            <IconContainer success>
              <CheckCircle size={32} />
            </IconContainer>
            <Title>Xác thực thành công!</Title>
            <Message>{message}</Message>
            <ActionButton to="/dashboard">
              Đi đến Dashboard
              <ArrowRight size={16} />
            </ActionButton>
          </>
        );

      case 'error':
        return (
          <>
            <IconContainer error>
              <XCircle size={32} />
            </IconContainer>
            <Title>Xác thực thất bại</Title>
            <Message>{message}</Message>
            <ActionButton to="/register">
              Quay lại đăng ký
              <ArrowRight size={16} />
            </ActionButton>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <VerifyContainer>
      <VerifyCard>
        {renderContent()}
      </VerifyCard>
    </VerifyContainer>
  );
};

export default VerifyEmailPage;
